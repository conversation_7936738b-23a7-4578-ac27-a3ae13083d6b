# Udesk客户管理接口实现

## 任务概述
在yyt-common-udesk模块中实现Udesk客户管理接口，包括创建、更新、删除客户功能，基于Udesk API v2文档。

## 实现计划
1. ✅ 更新pom.xml添加必要依赖
2. ✅ 创建DTO类
   - UdeskApiResponse - 通用API响应
   - UdeskCustomer - 客户信息实体
   - CreateCustomerRequest - 创建客户请求
   - CreateCustomerResponse - 创建客户响应
   - UpdateCustomerRequest - 更新客户请求
   - UpdateCustomerResponse - 更新客户响应
   - DeleteCustomerRequest - 删除客户请求
   - DeleteCustomerResponse - 删除客户响应
   - CustomerQueryCondition - 客户查询条件
3. ✅ 创建工具类
   - UdeskSignUtil - API签名工具
4. ✅ 更新配置类
   - UdeskProperties - 配置属性
   - UdeskAutoConfiguration - 自动配置
5. ✅ 实现Service层
   - UdeskService - 服务接口（创建、更新、删除）
   - UdeskServiceImpl - 服务实现
6. ✅ 创建配置文件
   - spring.factories - 自动配置
   - README.md - 使用文档

## 核心功能
- 支持Udesk API v2签名认证（SHA256/SHA1）
- 正确的签名算法：email + apiToken + timestamp + nonce + sign_version
- 创建客户功能，支持完整的客户信息
- 更新客户功能，支持多种查询条件
- 删除客户功能，支持多种查询条件
- 支持多种客户标识方式（ID、邮箱、电话、token等）
- 基于RestTemplate的HTTP客户端
- 完善的异常处理和日志记录
- 自动配置，开箱即用

## 配置示例
```yaml
udesk:
  email: <EMAIL>
  api-token: your-api-token
  domain: https://your-domain.udesk.cn
```

## 使用示例

### 创建客户
```java
@Autowired
private UdeskService udeskService;

CreateCustomerRequest request = new CreateCustomerRequest();
CreateCustomerRequest.CustomerInfo customer = new CreateCustomerRequest.CustomerInfo();
customer.setNickName("测试客户");
customer.setEmail("<EMAIL>");
request.setCustomer(customer);

CreateCustomerResponse response = udeskService.createCustomer(request);
```

### 更新客户
```java
UpdateCustomerRequest request = new UpdateCustomerRequest();
request.setCondition(CustomerQueryCondition.byEmail("<EMAIL>"));

UpdateCustomerRequest.CustomerInfo customer = new UpdateCustomerRequest.CustomerInfo();
customer.setNickName("更新后的客户名称");
customer.setLevel("vip");
request.setCustomer(customer);

UpdateCustomerResponse response = udeskService.updateCustomer(request);
```

### 删除客户
```java
DeleteCustomerRequest request = DeleteCustomerRequest.byId(12345L);
DeleteCustomerResponse response = udeskService.deleteCustomer(request);
```

## 实现状态
✅ 已完成 - 2025-01-27
🔧 已修正鉴权机制 - 2025-01-27

### 修正内容
- 修正签名算法：使用正确的 `email + apiToken + timestamp + nonce + sign_version` 格式
- 配置属性调整：将 `shareSecret` 改为 `apiToken`
- 支持SHA256/SHA1双重签名算法
- 更新文档和配置说明

## 文件清单
- pom.xml - 依赖配置
- dto/ - 数据传输对象
  - UdeskApiResponse.java - 通用API响应
  - UdeskCustomer.java - 客户信息实体
  - CreateCustomerRequest.java - 创建客户请求
  - CreateCustomerResponse.java - 创建客户响应
  - UpdateCustomerRequest.java - 更新客户请求
  - UpdateCustomerResponse.java - 更新客户响应
  - DeleteCustomerRequest.java - 删除客户请求
  - DeleteCustomerResponse.java - 删除客户响应
  - CustomerQueryCondition.java - 客户查询条件
- config/ - 配置类
  - UdeskProperties.java - 配置属性
  - UdeskAutoConfiguration.java - 自动配置
- service/ - 服务接口和实现
  - UdeskService.java - 服务接口
  - impl/UdeskServiceImpl.java - 服务实现
- util/ - 工具类
  - UdeskSignUtil.java - API签名工具
- README.md - 使用文档
- spring.factories - 自动配置
