# Udesk创建客户接口实现

## 任务概述
在yyt-common-udesk模块中实现Udesk创建客户接口，基于Udesk API v2文档。

## 实现计划
1. ✅ 更新pom.xml添加必要依赖
2. ✅ 创建DTO类
   - UdeskApiResponse - 通用API响应
   - UdeskCustomer - 客户信息实体
   - CreateCustomerRequest - 创建客户请求
   - CreateCustomerResponse - 创建客户响应
3. ✅ 创建工具类
   - UdeskSignUtil - API签名工具
4. ✅ 更新配置类
   - UdeskProperties - 配置属性
   - UdeskAutoConfiguration - 自动配置
5. ✅ 实现Service层
   - UdeskService - 服务接口
   - UdeskServiceImpl - 服务实现
6. ✅ 创建配置文件
   - spring.factories - 自动配置
   - README.md - 使用文档

## 核心功能
- 支持Udesk API v2签名认证（HMAC-SHA256）
- 创建客户功能，支持完整的客户信息
- 基于RestTemplate的HTTP客户端
- 自动配置，开箱即用

## 配置示例
```yaml
udesk:
  email: <EMAIL>
  share-secret: your-share-secret
  domain: https://your-domain.udesk.cn
```

## 使用示例
```java
@Autowired
private UdeskService udeskService;

CreateCustomerRequest request = new CreateCustomerRequest();
CreateCustomerRequest.CustomerInfo customer = new CreateCustomerRequest.CustomerInfo();
customer.setNickName("测试客户");
customer.setEmail("<EMAIL>");
request.setCustomer(customer);

CreateCustomerResponse response = udeskService.createCustomer(request);
```

## 实现状态
✅ 已完成 - 2025-01-27

## 文件清单
- pom.xml - 依赖配置
- dto/ - 数据传输对象
- config/ - 配置类
- service/ - 服务接口和实现
- util/ - 工具类
- README.md - 使用文档
- spring.factories - 自动配置
