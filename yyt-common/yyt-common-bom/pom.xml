<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>group.springframework</groupId>
        <artifactId>spring-cloud-dependencies-parent</artifactId>
        <version>2024.0.0</version>
        <relativePath/>
    </parent>

    <groupId>com.yts</groupId>
    <artifactId>yyt-common-bom</artifactId>
    <packaging>pom</packaging>
    <version>${yyt.version}</version>
    <description>yyt 公共版本控制，在此pom定义的依赖，全局业务微服务不需要指定版本。</description>

    <properties>
        <yyt.version>5.7.0</yyt.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mybatis-plus-join.version>1.5.2</mybatis-plus-join.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <druid.version>1.2.23</druid.version>
        <hutool.version>5.8.34</hutool.version>
        <mysql.connector.version>8.3.0</mysql.connector.version>
        <oracle.version>********</oracle.version>
        <sqlserver.version>8.4.1.jre8</sqlserver.version>
        <dm.version>*********</dm.version>
        <highgo.version>6.2.0</highgo.version>
        <knife4j.version>3.0.5</knife4j.version>
        <springdoc.version>2.1.0</springdoc.version>
        <swagger.core.version>2.2.14</swagger.core.version>
        <mp.weixin.version>4.6.7.B</mp.weixin.version>
        <ijpay.version>2.9.10-17</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <javax.version>4.0.1</javax.version>
        <jsoup.version>1.15.3</jsoup.version>
        <aviator.version>5.4.3</aviator.version>
        <flowable.version>7.0.0</flowable.version>
        <security.oauth.version>2.5.2.RELEASE</security.oauth.version>
        <fastjson.version>1.2.83</fastjson.version>
        <xxl.job.version>2.4.2</xxl.job.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <aws.version>1.12.261</aws.version>
        <javers.version>7.3.3</javers.version>
        <seata.version>1.7.0</seata.version>
        <asm.version>7.1</asm.version>
        <sensitive.word.version>0.21.0</sensitive.word.version>
        <log4j2.version>2.17.1</log4j2.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <cloud.plugin.version>1.0.0</cloud.plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
        <anyline.version>8.7.2-jdk17-20240808</anyline.version>
        <sms4j.version>3.2.1</sms4j.version>
        <jakarta.mail.version>2.0.1</jakarta.mail.version>
        <dashscope.version>2.16.5</dashscope.version>
        <velocity.version>2.3</velocity.version>
        <common.io.version>2.16.1</common.io.version>
        <velocity.tool.version>3.1</velocity.tool.version>
        <lock4j.redis.template>2.2.5</lock4j.redis.template>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-core</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-audit</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-data</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-gateway</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-gray</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-datasource</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-idempotent</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-job</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-log</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-oss</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-security</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-sensitive</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-sentinel</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-feign</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-sequence</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-swagger</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-eid</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-ocr</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-pay-yop</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-pay-wx</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-pay-huifu</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-logistics</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-agora</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-tencent</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-seata</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-ess</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-xss</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-sse</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-websocket</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-encrypt-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-excel</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-test</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-alert</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-upms-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-user-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-distribution-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-order-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-goods-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-merchant-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-ticket-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-app-server-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-pay-platform-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-flow-task-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-flow-engine-api</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-smallprogram</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-rocketmq</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-mq-idempotent</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-udesk</artifactId>
                <version>${yyt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-es</artifactId>
                <version>${yyt.version}</version>
            </dependency>
            <!-- lock4j分布式事务锁 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
                <version>${lock4j.redis.template}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <!-- 必备：敏感词-->
            <dependency>
                <groupId>com.github.houbb</groupId>
                <artifactId>sensitive-word</artifactId>
                <version>${sensitive.word.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-annotation</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- druid 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--mysql 驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <!--oracle 驱动-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <!-- mssql -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!--DM8-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate6.1</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.highgo</groupId>
                <artifactId>HgdbJdbc</artifactId>
                <version>${highgo.version}</version>
            </dependency>
            <!--anyline-->
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-environment-spring-data-jdbc</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-mysql</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-oracle</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-postgresql</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-dm</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- commons.io -->
            <dependency>
                <artifactId>commons-io</artifactId>
                <groupId>commons-io</groupId>
                <version>${common.io.version}</version>
            </dependency>
            <!--计算引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <!-- 对象对比工具-->
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>${javers.version}</version>
            </dependency>
            <!--springdoc -->
            <dependency>
                <groupId>io.springboot</groupId>
                <artifactId>knife4j-openapi3-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!--springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!--微信依赖-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${mp.weixin.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <!--支付相关SDK-->
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-WxPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-AliPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <!--定义groovy 版本-->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
            <!--jsoup html 解析组件-->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <!--工作流依赖-->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 短信发送工具类 -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!--  邮件发送工具 -->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${jakarta.mail.version}</version>
            </dependency>
            <!--  阿里云百炼工具 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>${dashscope.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-simple</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--webhook-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-oa-core</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!-- velocity -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity.tools</groupId>
                <artifactId>velocity-tools-generic</artifactId>
                <version>${velocity.tool.version}</version>
            </dependency>
            <!--  指定 log4j 版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--hutool bom-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>


    </dependencyManagement>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
