<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, yyt All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: yyt
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yts</groupId>
		<artifactId>yyt</artifactId>
		<version>5.7.0</version>
	</parent>

	<artifactId>yyt-common</artifactId>
	<packaging>pom</packaging>

	<description>yyt 公共聚合模块</description>


    <modules>
		<module>yyt-common-audit</module>
		<module>yyt-common-bom</module>
		<module>yyt-common-core</module>
		<module>yyt-common-data</module>
		<module>yyt-common-datasource</module>
		<module>yyt-common-excel</module>
		<module>yyt-common-encrypt-api</module>
		<module>yyt-common-feign</module>
		<module>yyt-common-gateway</module>
		<module>yyt-common-gray</module>
		<module>yyt-common-idempotent</module>
		<module>yyt-common-job</module>
		<module>yyt-common-log</module>
		<module>yyt-common-oss</module>
		<module>yyt-common-seata</module>
		<module>yyt-common-security</module>
		<module>yyt-common-sensitive</module>
		<module>yyt-common-sentinel</module>
		<module>yyt-common-sequence</module>
		<module>yyt-common-swagger</module>
		<module>yyt-common-sse</module>
		<module>yyt-common-websocket</module>
		<module>yyt-common-xss</module>
        <module>yyt-common-ocr</module>
        <module>yyt-common-eid</module>
		<module>yyt-common-pay-yop</module>
		<module>yyt-common-logistics</module>
		<module>yyt-common-pay-wx</module>
		<module>yyt-common-test</module>
        <module>yyt-common-ess</module>
		<module>yyt-common-smallprogram</module>
        <module>yyt-common-tencent</module>
        <module>yyt-common-agora</module>
        <module>yyt-common-antchain</module>
        <module>yyt-common-pay-huifu</module>
        <module>yyt-common-alert</module>
        <module>yyt-common-rocketmq</module>
        <module>yyt-common-es</module>
		<module>yyt-common-mq-idempotent</module>
        <module>yyt-common-udesk</module>
    </modules>
	<dependencies>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
		</dependency>
	</dependencies>
</project>
