package com.yts.yyt.common.udesk.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.yts.yyt.common.udesk.config.UdeskProperties;
import com.yts.yyt.common.udesk.dto.*;
import com.yts.yyt.common.udesk.service.UdeskService;
import com.yts.yyt.common.udesk.util.UdeskSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * Udesk服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UdeskServiceImpl implements UdeskService {

    private final UdeskProperties udeskProperties;
    private final RestTemplate restTemplate;

    @Override
    public CreateCustomerResponse createCustomer(CreateCustomerRequest request) {
        log.info("开始创建Udesk客户，请求参数：{}", JSONUtil.toJsonStr(request));

        try {
            // 构建API URL
            String url = UdeskSignUtil.buildApiUrl(
                    udeskProperties.getDomain(),
                    "/customers",
                    udeskProperties.getEmail(),
                    udeskProperties.getShareSecret()
            );

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            HttpEntity<CreateCustomerRequest> entity = new HttpEntity<>(request, headers);

            log.info("发送创建客户请求到：{}", url);
            log.debug("请求体：{}", JSONUtil.toJsonStr(request));

            // 发送POST请求
            ResponseEntity<CreateCustomerResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    CreateCustomerResponse.class
            );

            CreateCustomerResponse result = response.getBody();
            log.info("创建客户响应：{}", JSONUtil.toJsonStr(result));

            if (result != null && result.isSuccess()) {
                log.info("创建客户成功，客户ID：{}", result.getCustomer() != null ? result.getCustomer().getId() : "未知");
            } else {
                log.error("创建客户失败，错误码：{}，错误信息：{}",
                         result != null ? result.getCode() : "未知",
                         result != null ? result.getMessage() : "未知");
            }

            return result;

        } catch (Exception e) {
            log.error("创建Udesk客户异常", e);
            CreateCustomerResponse errorResponse = new CreateCustomerResponse();
            errorResponse.setCode(-1);
            errorResponse.setMessage("创建客户异常：" + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public UpdateCustomerResponse updateCustomer(UpdateCustomerRequest request) {
        log.info("开始更新Udesk客户，请求参数：{}", JSONUtil.toJsonStr(request));

        try {
            // 验证查询条件
            if (request.getCondition() == null || StrUtil.isBlank(request.getCondition().getType())
                || StrUtil.isBlank(request.getCondition().getContent())) {
                UpdateCustomerResponse errorResponse = new UpdateCustomerResponse();
                errorResponse.setCode(-1);
                errorResponse.setMessage("查询条件不能为空");
                return errorResponse;
            }

            // 构建API URL
            String baseUrl = UdeskSignUtil.buildApiUrl(
                    udeskProperties.getDomain(),
                    "/customers/update_customer",
                    udeskProperties.getEmail(),
                    udeskProperties.getShareSecret()
            );

            // 添加查询参数
            String url = baseUrl + "&type=" + request.getCondition().getType()
                       + "&content=" + request.getCondition().getContent();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体（不包含查询条件）
            UpdateCustomerRequest requestBody = new UpdateCustomerRequest();
            requestBody.setCustomer(request.getCustomer());
            requestBody.setOtherEmails(request.getOtherEmails());
            requestBody.setTags(request.getTags());

            HttpEntity<UpdateCustomerRequest> entity = new HttpEntity<>(requestBody, headers);

            log.info("发送更新客户请求到：{}", url);
            log.debug("请求体：{}", JSONUtil.toJsonStr(requestBody));

            // 发送PUT请求
            ResponseEntity<UpdateCustomerResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.PUT,
                    entity,
                    UpdateCustomerResponse.class
            );

            UpdateCustomerResponse result = response.getBody();
            log.info("更新客户响应：{}", JSONUtil.toJsonStr(result));

            if (result != null && result.isSuccess()) {
                log.info("更新客户成功，客户ID：{}", result.getCustomer() != null ? result.getCustomer().getId() : "未知");
            } else {
                log.error("更新客户失败，错误码：{}，错误信息：{}",
                         result != null ? result.getCode() : "未知",
                         result != null ? result.getMessage() : "未知");
            }

            return result;

        } catch (Exception e) {
            log.error("更新Udesk客户异常", e);
            UpdateCustomerResponse errorResponse = new UpdateCustomerResponse();
            errorResponse.setCode(-1);
            errorResponse.setMessage("更新客户异常：" + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public DeleteCustomerResponse deleteCustomer(DeleteCustomerRequest request) {
        log.info("开始删除Udesk客户，请求参数：{}", JSONUtil.toJsonStr(request));

        try {
            // 验证查询条件
            if (request.getCondition() == null || StrUtil.isBlank(request.getCondition().getType())
                || StrUtil.isBlank(request.getCondition().getContent())) {
                DeleteCustomerResponse errorResponse = new DeleteCustomerResponse();
                errorResponse.setCode(-1);
                errorResponse.setMessage("查询条件不能为空");
                return errorResponse;
            }

            // 构建API URL
            String baseUrl = UdeskSignUtil.buildApiUrl(
                    udeskProperties.getDomain(),
                    "/customers/destroy_customer",
                    udeskProperties.getEmail(),
                    udeskProperties.getShareSecret()
            );

            // 添加查询参数
            String url = baseUrl + "&type=" + request.getCondition().getType()
                       + "&content=" + request.getCondition().getContent();

            log.info("发送删除客户请求到：{}", url);

            // 发送DELETE请求
            ResponseEntity<DeleteCustomerResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    null,
                    DeleteCustomerResponse.class
            );

            DeleteCustomerResponse result = response.getBody();
            log.info("删除客户响应：{}", JSONUtil.toJsonStr(result));

            if (result != null && result.isSuccess()) {
                log.info("删除客户成功，客户ID：{}", result.getCustomerId());
            } else {
                log.error("删除客户失败，错误码：{}，错误信息：{}",
                         result != null ? result.getCode() : "未知",
                         result != null ? result.getMessage() : "未知");
            }

            return result;

        } catch (Exception e) {
            log.error("删除Udesk客户异常", e);
            DeleteCustomerResponse errorResponse = new DeleteCustomerResponse();
            errorResponse.setCode(-1);
            errorResponse.setMessage("删除客户异常：" + e.getMessage());
            return errorResponse;
        }
    }
}
