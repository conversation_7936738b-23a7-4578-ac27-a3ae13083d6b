package com.yts.yyt.common.udesk.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建客户响应结果
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateCustomerResponse extends UdeskApiResponse<UdeskCustomer> {

    /**
     * 客户信息
     */
    private UdeskCustomer customer;

    /**
     * 获取客户信息
     */
    public UdeskCustomer getCustomer() {
        return customer != null ? customer : getData();
    }
}
