package com.yts.yyt.common.udesk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Udesk配置属性
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@ConfigurationProperties(prefix = "udesk")
public class UdeskProperties {

    /**
     * API邮箱（用于签名认证）
     */
    private String email;

    /**
     * 共享密钥（用于签名认证）
     */
    private String shareSecret;

    /**
     * Udesk域名，例如：https://demo.udesk.cn
     */
    private String domain;

    /**
     * 连接超时时间（毫秒），默认5秒
     */
    private int connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒），默认10秒
     */
    private int readTimeout = 10000;
}
