package com.yts.yyt.common.udesk.config;

import com.yts.yyt.common.udesk.service.UdeskService;
import com.yts.yyt.common.udesk.service.impl.UdeskServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Udesk自动配置类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Configuration
@EnableConfigurationProperties(UdeskProperties.class)
public class UdeskAutoConfiguration {

    /**
     * 注册UdeskService Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public UdeskService udeskService(UdeskProperties udeskProperties, RestTemplate restTemplate) {
        return new UdeskServiceImpl(udeskProperties, restTemplate);
    }
}
