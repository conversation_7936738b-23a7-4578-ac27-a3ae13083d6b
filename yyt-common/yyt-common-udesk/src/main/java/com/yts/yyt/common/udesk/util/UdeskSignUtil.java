package com.yts.yyt.common.udesk.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;

import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * Udesk API签名工具类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class UdeskSignUtil {

    /**
     * 生成Udesk API v2签名
     *
     * @param email       API邮箱
     * @param shareSecret 共享密钥
     * @param timestamp   时间戳
     * @param nonce       随机字符串
     * @return 签名
     */
    public static String generateSignV2(String email, String shareSecret, long timestamp, String nonce) {
        // 构建签名字符串：email + shareSecret + timestamp + nonce
        String signStr = email + shareSecret + timestamp + nonce;
        
        // 使用HMAC-SHA256算法生成签名
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, shareSecret.getBytes());
        return hMac.digestHex(signStr);
    }

    /**
     * 生成随机nonce字符串
     *
     * @return nonce
     */
    public static String generateNonce() {
        return UUID.randomUUID().toString();
    }

    /**
     * 获取当前时间戳（秒）
     *
     * @return 时间戳
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 构建API请求参数
     *
     * @param email       API邮箱
     * @param shareSecret 共享密钥
     * @return 请求参数Map
     */
    public static Map<String, String> buildApiParams(String email, String shareSecret) {
        long timestamp = getCurrentTimestamp();
        String nonce = generateNonce();
        String sign = generateSignV2(email, shareSecret, timestamp, nonce);

        Map<String, String> params = new TreeMap<>();
        params.put("email", email);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("nonce", nonce);
        params.put("sign", sign);
        params.put("sign_version", "v2");

        return params;
    }

    /**
     * 构建查询字符串
     *
     * @param params 参数Map
     * @return 查询字符串
     */
    public static String buildQueryString(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }

    /**
     * 构建完整的API URL
     *
     * @param baseUrl     基础URL
     * @param path        API路径
     * @param email       API邮箱
     * @param shareSecret 共享密钥
     * @return 完整URL
     */
    public static String buildApiUrl(String baseUrl, String path, String email, String shareSecret) {
        Map<String, String> params = buildApiParams(email, shareSecret);
        String queryString = buildQueryString(params);
        
        String url = baseUrl;
        if (!url.endsWith("/")) {
            url += "/";
        }
        url += "open_api_v1" + path;
        
        if (StrUtil.isNotBlank(queryString)) {
            url += "?" + queryString;
        }
        
        return url;
    }
}
