package com.yts.yyt.common.udesk.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 更新客户响应结果
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateCustomerResponse extends UdeskApiResponse<UdeskCustomer> {

    /**
     * 客户信息
     */
    private UdeskCustomer customer;

    /**
     * 获取客户信息
     */
    public UdeskCustomer getCustomer() {
        return customer != null ? customer : getData();
    }
}
