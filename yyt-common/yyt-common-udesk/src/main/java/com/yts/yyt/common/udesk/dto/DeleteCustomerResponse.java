package com.yts.yyt.common.udesk.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 删除客户响应结果
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeleteCustomerResponse extends UdeskApiResponse<Long> {

    /**
     * 删除的客户ID
     */
    private Long customerId;

    /**
     * 获取删除的客户ID
     */
    public Long getCustomerId() {
        return customerId != null ? customerId : getData();
    }
}
