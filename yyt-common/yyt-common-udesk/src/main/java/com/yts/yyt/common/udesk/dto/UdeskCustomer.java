package com.yts.yyt.common.udesk.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Udesk客户信息实体
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class UdeskCustomer {

    /**
     * 客户ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 客户等级：normal-普通，vip-VIP
     */
    private String level;

    /**
     * 描述
     */
    private String description;

    /**
     * 负责客服ID
     */
    private Long ownerId;

    /**
     * 负责客服组ID
     */
    private Long ownerGroupId;

    /**
     * 自定义字段
     */
    private Map<String, Object> customFields;

    /**
     * 外部唯一标识
     */
    private String openApiToken;

    /**
     * 客户公司ID
     */
    private Long organizationId;

    /**
     * 是否被加入黑名单
     */
    private Boolean isBlocked;

    /**
     * 客户web标识
     */
    private String webToken;

    /**
     * 客户sdk标识
     */
    private String sdkToken;

    /**
     * 标签列表
     */
    private List<UdeskTag> tags;

    /**
     * 富文本标签列表
     */
    private List<UdeskRichTag> richTags;

    /**
     * 首次联系时间
     */
    private LocalDateTime firstContactAt;

    /**
     * 最后联系时间
     */
    private LocalDateTime lastContactAt;

    /**
     * 首次电话联系时间
     */
    private LocalDateTime firstContactAtViaPhone;

    /**
     * 最后电话联系时间
     */
    private LocalDateTime lastContactAtViaPhone;

    /**
     * 首次在线客服联系时间
     */
    private LocalDateTime firstContactAtViaIm;

    /**
     * 最后在线客服联系时间
     */
    private LocalDateTime lastContactAtViaIm;

    /**
     * 主邮箱
     */
    private String email;

    /**
     * 其他邮箱列表
     */
    private List<String[]> otherEmails;

    /**
     * 联系电话列表
     */
    private List<UdeskCellphone> cellphones;

    /**
     * 创建渠道中文名称
     */
    private String platform;

    /**
     * 客户来源中文名称
     */
    private String sourceChannel;

    /**
     * 微信信息
     */
    private List<UdeskWeixin> weixins;

    /**
     * 微信小程序信息
     */
    private List<UdeskWeixinMini> weixinMinis;

    /**
     * 企业微信信息
     */
    private List<UdeskWeixinWork> weixinWorks;

    /**
     * 微信客服信息
     */
    private List<UdeskWeixinKf> weixinKfs;

    /**
     * 标签信息
     */
    @Data
    public static class UdeskTag {
        private Long id;
        private String name;
        private Long companyId;
    }

    /**
     * 富文本标签信息
     */
    @Data
    public static class UdeskRichTag {
        private Long id;
        private String name;
        private String color;
        private Long companyId;
    }

    /**
     * 电话信息
     */
    @Data
    public static class UdeskCellphone {
        private Long id;
        private String content;
    }

    /**
     * 微信信息
     */
    @Data
    public static class UdeskWeixin {
        private String appid;
        private String openid;
        private String unionid;
    }

    /**
     * 微信小程序信息
     */
    @Data
    public static class UdeskWeixinMini {
        private String appid;
        private String openid;
        private String unionid;
    }

    /**
     * 企业微信信息
     */
    @Data
    public static class UdeskWeixinWork {
        private String agentid;
        private String corpid;
        private String userid;
        private String openUserid;
    }

    /**
     * 微信客服信息
     */
    @Data
    public static class UdeskWeixinKf {
        private String externalUserid;
        private String openKfid;
        private String unionid;
    }
}
