package com.yts.yyt.common.udesk.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录响应结果
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginResponse extends UdeskApiResponse<String> {

    /**
     * API鉴权Token
     */
    private String openApiAuthToken;

    /**
     * 获取API Token
     */
    public String getOpenApiAuthToken() {
        return openApiAuthToken != null ? openApiAuthToken : getData();
    }
}
