package com.yts.yyt.common.udesk.dto;

import lombok.Data;

/**
 * 客户查询条件
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class CustomerQueryCondition {

    /**
     * 条件类型
     */
    private String type;

    /**
     * 条件内容
     */
    private String content;

    /**
     * 条件类型枚举
     */
    public enum Type {
        /**
         * 客户ID
         */
        ID("id"),
        /**
         * 客户邮箱
         */
        EMAIL("email"),
        /**
         * 客户电话
         */
        CELLPHONE("cellphone"),
        /**
         * 客户外部唯一标识
         */
        TOKEN("token"),
        /**
         * 客户微信openid
         */
        WEIXIN_OPEN_ID("weixin_open_id"),
        /**
         * 客户微信小程序openid
         */
        WEIXIN_MINI_OPENID("weixin_mini_openid"),
        /**
         * 客户企业微信的唯一标识
         */
        WEIXIN_WORK_IDENTIFIER("weixin_work_identifier"),
        /**
         * 客户微博openid
         */
        WEIBO_ID("weibo_id"),
        /**
         * 客户sdk标识
         */
        SDK_TOKEN("sdk_token"),
        /**
         * 客户web标识
         */
        WEB_TOKEN("web_token");

        private final String value;

        Type(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 构造函数
     */
    public CustomerQueryCondition() {
    }

    /**
     * 构造函数
     *
     * @param type    条件类型
     * @param content 条件内容
     */
    public CustomerQueryCondition(String type, String content) {
        this.type = type;
        this.content = content;
    }

    /**
     * 构造函数
     *
     * @param type    条件类型枚举
     * @param content 条件内容
     */
    public CustomerQueryCondition(Type type, String content) {
        this.type = type.getValue();
        this.content = content;
    }

    /**
     * 根据客户ID创建查询条件
     */
    public static CustomerQueryCondition byId(Long id) {
        return new CustomerQueryCondition(Type.ID, String.valueOf(id));
    }

    /**
     * 根据邮箱创建查询条件
     */
    public static CustomerQueryCondition byEmail(String email) {
        return new CustomerQueryCondition(Type.EMAIL, email);
    }

    /**
     * 根据电话创建查询条件
     */
    public static CustomerQueryCondition byCellphone(String cellphone) {
        return new CustomerQueryCondition(Type.CELLPHONE, cellphone);
    }

    /**
     * 根据外部标识创建查询条件
     */
    public static CustomerQueryCondition byToken(String token) {
        return new CustomerQueryCondition(Type.TOKEN, token);
    }

    /**
     * 根据微信openid创建查询条件
     */
    public static CustomerQueryCondition byWeixinOpenId(String openId) {
        return new CustomerQueryCondition(Type.WEIXIN_OPEN_ID, openId);
    }
}
