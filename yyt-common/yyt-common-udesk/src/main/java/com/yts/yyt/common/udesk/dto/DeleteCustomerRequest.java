package com.yts.yyt.common.udesk.dto;

import lombok.Data;

/**
 * 删除客户请求参数
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class DeleteCustomerRequest {

    /**
     * 查询条件
     */
    private CustomerQueryCondition condition;

    /**
     * 构造函数
     */
    public DeleteCustomerRequest() {
    }

    /**
     * 构造函数
     *
     * @param condition 查询条件
     */
    public DeleteCustomerRequest(CustomerQueryCondition condition) {
        this.condition = condition;
    }

    /**
     * 根据客户ID创建删除请求
     */
    public static DeleteCustomerRequest byId(Long id) {
        return new DeleteCustomerRequest(CustomerQueryCondition.byId(id));
    }

    /**
     * 根据邮箱创建删除请求
     */
    public static DeleteCustomerRequest byEmail(String email) {
        return new DeleteCustomerRequest(CustomerQueryCondition.byEmail(email));
    }

    /**
     * 根据电话创建删除请求
     */
    public static DeleteCustomerRequest byCellphone(String cellphone) {
        return new DeleteCustomerRequest(CustomerQueryCondition.byCellphone(cellphone));
    }

    /**
     * 根据外部标识创建删除请求
     */
    public static DeleteCustomerRequest byToken(String token) {
        return new DeleteCustomerRequest(CustomerQueryCondition.byToken(token));
    }
}
