package com.yts.yyt.common.udesk.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 创建客户请求参数
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class CreateCustomerRequest {

    /**
     * 客户信息
     */
    private CustomerInfo customer;

    /**
     * 其他邮箱列表
     */
    private List<String[]> otherEmails;

    /**
     * 标签列表，多个标签以逗号分隔
     */
    private String tags;

    /**
     * 客户信息
     */
    @Data
    public static class CustomerInfo {

        /**
         * 主邮箱地址
         */
        private String email;

        /**
         * 外部唯一标识
         */
        private String openApiToken;

        /**
         * 姓名（必填）
         */
        private String nickName;

        /**
         * 客户公司ID
         */
        private Long organizationId;

        /**
         * 描述
         */
        private String description;

        /**
         * 负责客服ID
         */
        private Long ownerId;

        /**
         * 负责客服组ID
         */
        private Long ownerGroupId;

        /**
         * 等级，默认为'normal'，取值：normal-普通，vip-VIP
         */
        private String level = "normal";

        /**
         * 是否加入黑名单，默认为false
         */
        private Boolean isBlocked = false;

        /**
         * 电话列表
         */
        private List<String[]> cellphones;

        /**
         * 微信列表
         */
        private List<WeixinInfo> weixins;

        /**
         * 微信小程序列表
         */
        private List<WeixinMiniInfo> weixinMinis;

        /**
         * 企业微信列表
         */
        private List<WeixinWorkInfo> weixinWorks;

        /**
         * 自定义字段
         */
        private Map<String, Object> customFields;

        /**
         * 客户web标识
         */
        private String webToken;
    }

    /**
     * 微信信息
     */
    @Data
    public static class WeixinInfo {
        /**
         * 执行动作：new-新增, delete-删除
         */
        private String action;

        /**
         * 微信应用ID
         */
        private String appid;

        /**
         * 客户的微信openid
         */
        private String openid;

        /**
         * 客户的微信unionid
         */
        private String unionid;
    }

    /**
     * 微信小程序信息
     */
    @Data
    public static class WeixinMiniInfo {
        /**
         * 执行动作：new-新增, delete-删除
         */
        private String action;

        /**
         * 微信小程序ID
         */
        private String appid;

        /**
         * 客户的微信小程序openid
         */
        private String openid;

        /**
         * 客户的微信小程序unionid
         */
        private String unionid;
    }

    /**
     * 企业微信信息
     */
    @Data
    public static class WeixinWorkInfo {
        /**
         * 执行动作：new-新增, delete-删除
         */
        private String action;

        /**
         * 企业微信应用ID
         */
        private String agentid;

        /**
         * 企业微信中的用户ID
         */
        private String userid;

        /**
         * 企业微信中的企业ID
         */
        private String corpid;

        /**
         * 企业微信供第三方识别的用户ID
         */
        private String openUserid;
    }
}
