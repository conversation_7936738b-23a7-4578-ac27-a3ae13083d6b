package com.yts.yyt.common.udesk.service;

import com.yts.yyt.common.udesk.config.UdeskTestConfiguration;
import com.yts.yyt.common.udesk.dto.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UdeskService测试类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@SpringBootTest
@ActiveProfiles("test")
@Import(UdeskTestConfiguration.class)
class UdeskServiceTest {

    @Autowired
    private UdeskService udeskService;

    @Test
    void testCreateCustomer() {
        System.out.println("=== 测试创建客户 ===");
        
        // 构建创建客户请求
        CreateCustomerRequest request = new CreateCustomerRequest();
        
        // 设置客户基本信息
        CreateCustomerRequest.CustomerInfo customer = new CreateCustomerRequest.CustomerInfo();
        customer.setNickName("测试客户_" + System.currentTimeMillis());
        customer.setEmail("test_" + System.currentTimeMillis() + "@example.com");
        customer.setLevel("normal");
        customer.setDescription("这是一个测试客户");
        customer.setIsBlocked(false);
        
        // 设置电话列表
        List<String[]> cellphones = new ArrayList<>();
        cellphones.add(new String[]{null, "13800138000"});
        cellphones.add(new String[]{null, "13900139000"});
        customer.setCellphones(cellphones);
        
        // 设置自定义字段
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("TextField_1", "测试文本字段");
        customFields.put("TextField_2", "多行文本内容1\r\n多行文本内容2");
        customer.setCustomFields(customFields);
        
        request.setCustomer(customer);
        request.setTags("测试标签1,测试标签2");
        
        // 发起请求
        System.out.println("发送创建客户请求...");
        CreateCustomerResponse response = udeskService.createCustomer(request);
        
        // 验证响应
        assertNotNull(response);
        System.out.println("响应码: " + response.getCode());
        System.out.println("响应消息: " + response.getMessage());
        
        if (response.isSuccess()) {
            assertNotNull(response.getCustomer());
            System.out.println("创建成功，客户ID: " + response.getCustomer().getId());
            System.out.println("客户姓名: " + response.getCustomer().getNickName());
        } else {
            System.out.println("创建失败: " + response.getMessage());
        }
    }

    @Test
    void testUpdateCustomer() {
        System.out.println("=== 测试更新客户 ===");
        
        // 构建更新客户请求
        UpdateCustomerRequest request = new UpdateCustomerRequest();
        
        // 设置查询条件（根据邮箱查找）
        request.setCondition(CustomerQueryCondition.byEmail("<EMAIL>"));
        
        // 设置要更新的客户信息
        UpdateCustomerRequest.CustomerInfo customer = new UpdateCustomerRequest.CustomerInfo();
        customer.setNickName("更新后的客户名称_" + System.currentTimeMillis());
        customer.setDescription("更新后的客户描述");
        customer.setLevel("vip");
        
        // 设置自定义字段
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("TextField_1", "更新后的文本字段");
        customer.setCustomFields(customFields);
        
        request.setCustomer(customer);
        
        // 发起请求
        System.out.println("发送更新客户请求...");
        UpdateCustomerResponse response = udeskService.updateCustomer(request);
        
        // 验证响应
        assertNotNull(response);
        System.out.println("响应码: " + response.getCode());
        System.out.println("响应消息: " + response.getMessage());
        
        if (response.isSuccess()) {
            assertNotNull(response.getCustomer());
            System.out.println("更新成功，客户ID: " + response.getCustomer().getId());
            System.out.println("客户姓名: " + response.getCustomer().getNickName());
        } else {
            System.out.println("更新失败: " + response.getMessage());
        }
    }

    @Test
    void testDeleteCustomer() {
        System.out.println("=== 测试删除客户 ===");
        
        // 构建删除客户请求（根据邮箱删除）
        DeleteCustomerRequest request = DeleteCustomerRequest.byEmail("<EMAIL>");
        
        // 发起请求
        System.out.println("发送删除客户请求...");
        DeleteCustomerResponse response = udeskService.deleteCustomer(request);
        
        // 验证响应
        assertNotNull(response);
        System.out.println("响应码: " + response.getCode());
        System.out.println("响应消息: " + response.getMessage());
        
        if (response.isSuccess()) {
            System.out.println("删除成功，客户ID: " + response.getCustomerId());
        } else {
            System.out.println("删除失败: " + response.getMessage());
        }
    }

    @Test
    void testDeleteCustomerById() {
        System.out.println("=== 测试根据ID删除客户 ===");
        
        // 构建删除客户请求（根据ID删除）
        DeleteCustomerRequest request = DeleteCustomerRequest.byId(12345L);
        
        // 发起请求
        System.out.println("发送删除客户请求...");
        DeleteCustomerResponse response = udeskService.deleteCustomer(request);
        
        // 验证响应
        assertNotNull(response);
        System.out.println("响应码: " + response.getCode());
        System.out.println("响应消息: " + response.getMessage());
        
        if (response.isSuccess()) {
            System.out.println("删除成功，客户ID: " + response.getCustomerId());
        } else {
            System.out.println("删除失败: " + response.getMessage());
        }
    }
}
