package com.yts.yyt.common.udesk.integration;

import com.yts.yyt.common.udesk.dto.*;
import com.yts.yyt.common.udesk.service.UdeskService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Udesk集成测试类
 * 
 * 注意：这些测试需要真实的Udesk配置才能运行
 * 请在application-test.yml中配置正确的email、api-token和domain
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@SpringBootTest
@ActiveProfiles("test")
@Disabled("需要真实的Udesk配置才能运行，请根据需要启用")
class UdeskIntegrationTest {

    @Autowired
    private UdeskService udeskService;

    @Test
    void testRealCreateCustomer() {
        System.out.println("=== 真实环境测试：创建客户 ===");
        
        CreateCustomerRequest request = new CreateCustomerRequest();
        
        CreateCustomerRequest.CustomerInfo customer = new CreateCustomerRequest.CustomerInfo();
        customer.setNickName("集成测试客户_" + System.currentTimeMillis());
        customer.setEmail("integration_test_" + System.currentTimeMillis() + "@example.com");
        customer.setLevel("normal");
        customer.setDescription("这是集成测试创建的客户");
        
        List<String[]> cellphones = new ArrayList<>();
        cellphones.add(new String[]{null, "13800138001"});
        customer.setCellphones(cellphones);
        
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("TextField_1", "集成测试文本");
        customer.setCustomFields(customFields);
        
        request.setCustomer(customer);
        request.setTags("集成测试,自动化测试");
        
        CreateCustomerResponse response = udeskService.createCustomer(request);
        
        System.out.println("创建客户结果:");
        System.out.println("成功: " + response.isSuccess());
        System.out.println("响应码: " + response.getCode());
        System.out.println("消息: " + response.getMessage());
        
        if (response.isSuccess() && response.getCustomer() != null) {
            System.out.println("客户ID: " + response.getCustomer().getId());
            System.out.println("客户姓名: " + response.getCustomer().getNickName());
            System.out.println("客户邮箱: " + response.getCustomer().getEmail());
        }
    }

    @Test
    void testRealUpdateCustomer() {
        System.out.println("=== 真实环境测试：更新客户 ===");
        
        UpdateCustomerRequest request = new UpdateCustomerRequest();
        request.setCondition(CustomerQueryCondition.byEmail("<EMAIL>"));
        
        UpdateCustomerRequest.CustomerInfo customer = new UpdateCustomerRequest.CustomerInfo();
        customer.setNickName("更新后的集成测试客户_" + System.currentTimeMillis());
        customer.setDescription("集成测试更新的描述");
        customer.setLevel("vip");
        
        request.setCustomer(customer);
        
        UpdateCustomerResponse response = udeskService.updateCustomer(request);
        
        System.out.println("更新客户结果:");
        System.out.println("成功: " + response.isSuccess());
        System.out.println("响应码: " + response.getCode());
        System.out.println("消息: " + response.getMessage());
        
        if (response.isSuccess() && response.getCustomer() != null) {
            System.out.println("客户ID: " + response.getCustomer().getId());
            System.out.println("客户姓名: " + response.getCustomer().getNickName());
        }
    }

    @Test
    void testRealDeleteCustomer() {
        System.out.println("=== 真实环境测试：删除客户 ===");
        
        DeleteCustomerRequest request = DeleteCustomerRequest.byEmail("<EMAIL>");
        
        DeleteCustomerResponse response = udeskService.deleteCustomer(request);
        
        System.out.println("删除客户结果:");
        System.out.println("成功: " + response.isSuccess());
        System.out.println("响应码: " + response.getCode());
        System.out.println("消息: " + response.getMessage());
        
        if (response.isSuccess()) {
            System.out.println("删除的客户ID: " + response.getCustomerId());
        }
    }
}
