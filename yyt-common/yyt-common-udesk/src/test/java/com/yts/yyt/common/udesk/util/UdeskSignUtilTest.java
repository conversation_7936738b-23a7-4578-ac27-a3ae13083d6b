package com.yts.yyt.common.udesk.util;

import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UdeskSignUtil测试类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
class UdeskSignUtilTest {

    @Test
    void testGenerateSignV2() {
        String email = "<EMAIL>";
        String apiToken = "233df89e-b4a2-42e0-89af-f295b1078686";
        long timestamp = 1494474404;
        String nonce = "2d931510-d99f-494a-8c67-87feb05e1594";

        String sign = UdeskSignUtil.generateSignV2(email, apiToken, timestamp, nonce);
        
        assertNotNull(sign);
        assertFalse(sign.isEmpty());
        System.out.println("Generated sign: " + sign);
    }

    @Test
    void testGenerateNonce() {
        String nonce1 = UdeskSignUtil.generateNonce();
        String nonce2 = UdeskSignUtil.generateNonce();
        
        assertNotNull(nonce1);
        assertNotNull(nonce2);
        assertNotEquals(nonce1, nonce2);
        System.out.println("Generated nonce1: " + nonce1);
        System.out.println("Generated nonce2: " + nonce2);
    }

    @Test
    void testGetCurrentTimestamp() {
        long timestamp = UdeskSignUtil.getCurrentTimestamp();
        
        assertTrue(timestamp > 0);
        System.out.println("Current timestamp: " + timestamp);
    }

    @Test
    void testBuildApiParams() {
        String email = "<EMAIL>";
        String apiToken = "test-token-123";
        
        Map<String, String> params = UdeskSignUtil.buildApiParams(email, apiToken);
        
        assertNotNull(params);
        assertEquals(email, params.get("email"));
        assertEquals("v2", params.get("sign_version"));
        assertNotNull(params.get("timestamp"));
        assertNotNull(params.get("nonce"));
        assertNotNull(params.get("sign"));
        
        System.out.println("API params: " + params);
    }

    @Test
    void testBuildApiUrl() {
        String baseUrl = "https://demo.udesk.cn";
        String path = "/customers";
        String email = "<EMAIL>";
        String apiToken = "test-token-123";
        
        String url = UdeskSignUtil.buildApiUrl(baseUrl, path, email, apiToken);
        
        assertNotNull(url);
        assertTrue(url.startsWith(baseUrl));
        assertTrue(url.contains("open_api_v1" + path));
        assertTrue(url.contains("email=" + email));
        assertTrue(url.contains("sign_version=v2"));
        
        System.out.println("Built URL: " + url);
    }
}
