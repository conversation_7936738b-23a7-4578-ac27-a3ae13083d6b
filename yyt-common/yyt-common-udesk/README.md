# YYT Common Udesk

Udesk客服系统集成模块，提供客户管理等功能。

## 功能特性

- 创建客户
- 更新客户
- 删除客户
- 支持Udesk API v2签名认证
- 支持客户基本信息、自定义字段、标签等完整功能
- 支持多种客户查询方式（ID、邮箱、电话、token等）
- 自动配置，开箱即用

## 快速开始

### 1. 添加依赖

在需要使用的模块中添加依赖：

```xml
<dependency>
    <groupId>com.yts</groupId>
    <artifactId>yyt-common-udesk</artifactId>
    <version>5.7.0</version>
</dependency>
```

### 2. 配置参数

在 `application.yml` 中添加配置：

```yaml
udesk:
  email: <EMAIL>    # API邮箱
  share-secret: your-share-secret      # 共享密钥
  domain: https://your-domain.udesk.cn # Udesk域名
  connect-timeout: 5000                # 连接超时时间（可选，默认5秒）
  read-timeout: 10000                  # 读取超时时间（可选，默认10秒）
```

### 3. 使用示例

#### 创建客户

```java
@Service
public class CustomerService {

    @Autowired
    private UdeskService udeskService;

    public void createCustomer() {
        // 构建请求参数
        CreateCustomerRequest request = new CreateCustomerRequest();

        CreateCustomerRequest.CustomerInfo customer = new CreateCustomerRequest.CustomerInfo();
        customer.setNickName("测试客户");
        customer.setEmail("<EMAIL>");
        customer.setLevel("normal");
        customer.setDescription("测试客户描述");

        request.setCustomer(customer);
        request.setTags("标签1,标签2");

        // 调用创建客户接口
        CreateCustomerResponse response = udeskService.createCustomer(request);

        if (response.isSuccess()) {
            System.out.println("创建成功，客户ID：" + response.getCustomer().getId());
        } else {
            System.out.println("创建失败：" + response.getMessage());
        }
    }
}
```

#### 更新客户

```java
public void updateCustomer() {
    // 构建更新请求
    UpdateCustomerRequest request = new UpdateCustomerRequest();

    // 设置查询条件（根据邮箱查找客户）
    request.setCondition(CustomerQueryCondition.byEmail("<EMAIL>"));

    // 设置要更新的客户信息
    UpdateCustomerRequest.CustomerInfo customer = new UpdateCustomerRequest.CustomerInfo();
    customer.setNickName("更新后的客户名称");
    customer.setDescription("更新后的描述");
    customer.setLevel("vip");

    request.setCustomer(customer);

    // 调用更新客户接口
    UpdateCustomerResponse response = udeskService.updateCustomer(request);

    if (response.isSuccess()) {
        System.out.println("更新成功，客户ID：" + response.getCustomer().getId());
    } else {
        System.out.println("更新失败：" + response.getMessage());
    }
}
```

#### 删除客户

```java
public void deleteCustomer() {
    // 根据客户ID删除
    DeleteCustomerRequest request = DeleteCustomerRequest.byId(12345L);

    // 或者根据邮箱删除
    // DeleteCustomerRequest request = DeleteCustomerRequest.byEmail("<EMAIL>");

    // 调用删除客户接口
    DeleteCustomerResponse response = udeskService.deleteCustomer(request);

    if (response.isSuccess()) {
        System.out.println("删除成功，客户ID：" + response.getCustomerId());
    } else {
        System.out.println("删除失败：" + response.getMessage());
    }
}
```

## API文档

### 创建客户

支持的客户信息字段：

- `nickName`: 客户姓名（必填）
- `email`: 主邮箱
- `level`: 客户等级（normal/vip）
- `description`: 描述
- `ownerId`: 负责客服ID
- `ownerGroupId`: 负责客服组ID
- `isBlocked`: 是否加入黑名单
- `cellphones`: 电话列表
- `customFields`: 自定义字段
- `weixins`: 微信信息
- `weixinMinis`: 微信小程序信息
- `weixinWorks`: 企业微信信息

详细的API参数说明请参考Udesk官方文档。

## 注意事项

1. 确保Udesk配置信息正确
2. API邮箱和共享密钥需要在Udesk后台获取
3. 客户姓名为必填字段
4. 建议在生产环境中配置适当的超时时间
